const VideoConferencing = {
  addMeeting: {
    url: `/v1.0/meeting`,
    method: "POST",
  },
  meetingList: {
    url: `/v1.0/meeting`,
    method: "GET",
  },
  updatePlanMeetings: (id) => ({
    url: `/v1.0/meeting/${id}`,
    method: "PUT",
  }),
  meetingRoomDetail: (id) => ({
    url: `/v1.0/basic-plan/${id}`,
    method: "GET",
  }),
  manageAttendance: {
    url: `/v1.0/attendance`,
    method: "POST",
  },
  updateManageAttendance: (id) => ({
    url: `/v1.0/attendance/${id}`,
    method: "PUT",
  }),
  verifyMeeting: (id) => ({
    url: `/v1.0/verify-meeting/${id}`,
    method: "POST",
  }),
  meetingDetail: (id) => ({
    url: `/v1.0/get-meeting/${id}`,
    method: "GET",
  }),
  deleteRecording: (id) => ({
    url: `/v1.0/remove-meeting-rec/${id}`,
    method: "PUT",
  }),
  getRecordingLinks: (id) => ({
    url: `/meeting/get/preSignRecording/url?meeting_id=${id}`,
    method: "GET",
  }),
  getUserRecordingLinks: () => ({
    url: `/v1.0/recorded/meeting/list`,
  }),
  getRecordingLinksOfAMeeting: (id) => ({
    url: `/meeting/recording/analysis/all?meeting_id=${id}`,
    method: "GET",
  }),
  // getTranscriptionData: (meetingId, recordingId, userId) => ({
  //   url: `/rtc/meeting/transcriptionAnalysis/get?meeting_id=${meetingId}&recording_id=${recordingId}&share_username=${userId}`,
  //   method: "GET",
  // }),
  getTranscriptionData: (meetingId, recordingId) => ({
    url: `/meeting/recording/analysis?meeting_id=${meetingId}&meeting_recording_id=${recordingId}`,
    method: "GET",
  }),
  getParticipantsLogs: (meetingId, sessionId) => ({
    url: `/v1.0/meeting/participants/log?meeting_id=${meetingId}&session_id=${sessionId}`,
    method: "GET",
  }),
  getAttendanceReport: (meetingId) => ({
    url: `/v1.0/meeting/participants/attendance?meeting_id=${meetingId}`,
    method: "GET",
  }),
  startTranscriptionAnalysis: () => ({
    url: `/meeting/complete/transcriptionAnalysisJob`,
    method: "POST",
  }),
  deleteMeetingRecording: () => ({
    url: `/v1.0/delete/meeting/recordings`,
    method: "POST",
  }),
  deleteSubRecording: () => ({
    url: `/v1.0/delete/recording`,
    method: "POST",
  }),
  getPersonalMeetingRoomId: () => ({
    url: `/meeting/personal/meetingRoom`,
    method: "GET",
  }),
};

export default VideoConferencing;
