import React from "react";
import ReactDOM from "react-dom/client";
import $ from "jquery";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
// import { PersistGate } from "redux-persist/integration/react";
// import { Provider } from "react-redux";
// import App from "./App";
import Main from "./main";
import reportWebVitals from "./reportWebVitals";
import "./utils/i18n";
// import store, { Persistor } from "./redux/store";

window.jQuery = $;
require("bootstrap");

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  // <React.StrictMode>
    <BrowserRouter>
      {/* <Provider store={store}>
        <PersistGate persistor={Persistor}>
          <App />
        </PersistGate>
      </Provider> */}
      <Main />
    </BrowserRouter>
  // </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
