import React, { useRef, useEffect, useState } from "react";
import axios from "axios";
import {  useMaybeLayoutContext } from "@livekit/components-react";
import { CloseOutlined } from "@ant-design/icons";
import { Avatar, Progress } from "antd";
import { ChatsEntry } from "./ChatsEntry";
import { constants, DataReceivedEvent,TOPIC_NAME } from "../../utils/constants";
import { generateAvatar, generateUUID, getAvatarColor } from "../../utils/helper";
import { Endpoints } from "../../API/Endpoints/routes";
import { useParticipantContext } from "../../context/ParticipantContext";
import { cloneSingleChild } from "../../utils/cloneSingleChild";
import { ReactComponent as SendBtnIcon } from "./Assets/Send.svg";
import { ReactComponent as ImageIcon } from "./Assets/imageIcon.svg";
import UploadIcon from "./Assets/clipIcon.png";
import { ReactComponent as PDFIcon } from "./Assets/pdfIcon.svg";
import { ReactComponent as DeleteIcon } from "./Assets/deleteIcon.svg";
import { ReactComponent as EmptyChat } from "./Assets/ChatEmpty.svg";

import { ReactComponent as UnpinMessageIco } from "./Assets/pinnedMessage.svg";

export default function PublicChats({
  messageFormatter,
  showprivatechatdrawer,
  setPublicChatUnreadMessagesCount,
  publicChatUnreadMessagesCount,
  showchatdrawer,
  chatMessages,
  setChatMessages,
  localparticipant,
  setToastNotification,
  setToastStatus,
  setShowToast,
  scrollToRepliedChat,
  highlightedMessage,
  messageRefs,
  // Pinned messages
  publicChatPinnedMessages,
  setPublicChatPinnedMessages,
  ...props
}) {
  const inputRef = useRef(null);
  const ulRef = useRef(null);

  const layoutContext = useMaybeLayoutContext();
  const lastReadMsgAt = useRef(0);
  const [uploadedfile, setUploadedFile] = useState(null);
  const [uploadedFileName, setUploadedFileName] = useState(null);
  const [replyMessage, setReplyMessage] = useState(null);
  const locale = navigator ? navigator.language : "en-US";
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState("");
  const [editingMessage, setEditingMessage] = useState(null); // or editingMessageId
  const [editingText, setEditingText] = useState("");
  const [messageText, setMessageText] = useState("");
  const { participantColors } = useParticipantContext();
  useEffect(() => {
    if ((uploadedfile && inputRef.current) || replyMessage) {
      inputRef.current.focus(); // Focus the textarea when file is uploaded or a chat is replied
    }
  }, [uploadedfile, replyMessage]);

  async function handleSubmit(event) {
    // unfocus inputRef
    inputRef.current.blur();
    event.preventDefault();
    let thumbnailUrl = null;
    if(editingMessage){
      // Send EDIT_MESSAGE event
      const encoder = new TextEncoder();
      const message = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.EDIT_MESSAGE,
          id: editingMessage.id,
          message: editingText,
          timestamp: Date.now(),
          mode: "public"
        })
      );
      localparticipant.publishData(message, {
        reliable: true,
      });
      setChatMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === editingMessage.id 
          ? { ...msg, message: editingText, editTimestamp: Date.now() } 
          : msg
        )
      );
      setEditingMessage(null);
      setEditingText("");
      setMessageText("");
      return;
    }
    if (
      (inputRef.current && inputRef.current.value.trim() !== "") ||
      uploadedfile !== null
    ) {
      if (uploadedfile !== null) {
        const formData = new FormData();
        formData.append("file", uploadedfile);
        const response = await axios.post(
          `${constants.STAG_BASE_URL}${Endpoints.send_chat_attachment.url}`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
            onUploadProgress: (progressEvent) => {
              const progressPercent = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              setUploadProgress(progressPercent);
              setUploadStatus("uploading");
              setUploadedFile(uploadedfile);
            },
          }
        )
        .catch((error) => {
          setToastNotification(error.message);
          setToastStatus("error");
          setShowToast(true);
          setUploadStatus("failed");
        });
        if (response.data.success === 1) {
          thumbnailUrl = response.data.data.url;
          const uploaded = true;
          setUploadStatus("uploaded");
          setUploadProgress(0);
          setUploadedFile(!uploaded);
          setToastNotification("File uploaded successfully");
          setToastStatus("success");
          setShowToast(true);
        } else {
          setToastNotification("Error uploading file");
          setToastStatus("error"); 
          setShowToast(true);
        }
      }

      const textmessage =
        thumbnailUrl !== null
          ? inputRef.current && inputRef.current.value.trim() !== ""
            ? `${thumbnailUrl} ${inputRef.current.value}`
            : thumbnailUrl
          : inputRef.current.value;

      const encoder = new TextEncoder();
      const msgId = generateUUID();
      const encodedMessage = encoder.encode(
        JSON.stringify({
          action: DataReceivedEvent.SEND_PUBLIC_MESSAGE,
          id: msgId,
          message: textmessage,
          timestamp: Date.now(),
          isReplied: replyMessage !== null,
          replyMessage,
          reactions: [],
        })
      );
      localparticipant.publishData(encodedMessage, {
        reliable: true,
      });
      localparticipant.sendText(textmessage, {
        topic: TOPIC_NAME.CHAT,
      });
      const mesg = {
        id: msgId,
        message: textmessage,
        timestamp: Date.now(),
        from: localparticipant,
        isReplied: replyMessage !== null,
        replyMessage,
        reactions: [],
      };

      chatMessages.push(mesg);
      setChatMessages([...chatMessages]);
      inputRef.current.value = "";
      inputRef.current.style.height = "41px";
      setUploadedFile(null);
      setReplyMessage(null);
      thumbnailUrl = null;
      inputRef.current.focus();
      setMessageText("");
    }
  }

  useEffect(() => {
    if (ulRef.current) {
      ulRef.current.scrollTop = ulRef.current.scrollHeight;
    }
  }, [chatMessages]);

  useEffect(() => {
    if (!layoutContext || chatMessages?.length === 0) {
      return;
    }

    if (
      layoutContext.widget.state?.showChat &&
      chatMessages?.length > 0 &&
      lastReadMsgAt.current !== chatMessages[chatMessages?.length - 1]?.timestamp
    ) {
      lastReadMsgAt.current = chatMessages[chatMessages?.length - 1]?.timestamp;
      return;
    }

    const unreadMessageCount = chatMessages.filter(
      (msg) => !lastReadMsgAt.current || msg.timestamp > lastReadMsgAt.current
    )?.length;

    setPublicChatUnreadMessagesCount(unreadMessageCount);

    if (!showprivatechatdrawer && showchatdrawer) {
      setPublicChatUnreadMessagesCount(0);
      lastReadMsgAt.current = chatMessages[chatMessages?.length - 1]?.timestamp;
    }
  }, [chatMessages, layoutContext?.widget, showprivatechatdrawer]);

  const handleUpload = () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*,application/pdf";

    fileInput.onchange = (event) => {
      const file = event.target.files[0];

      if (file) {
        setUploadedFile(file);
      } else {
        setToastNotification("No file selected.");
        setToastStatus("error");
        setShowToast(true);
      }
    };

    fileInput.click();
  };

  useEffect(() => {
    if (uploadedfile) {
      setUploadedFileName(uploadedfile.name);
    }
  }, [uploadedfile]);

  const groupedMessages = chatMessages.reduce((acc, msg) => {
    const lastGroup = acc[acc.length - 1];
    if (lastGroup && lastGroup.from === msg.from) {
      lastGroup.messages.push(msg);
    } else {
      acc.push({ from: msg.from, messages: [msg] });
    }
    return acc;
  }, []);
  
  return (
    <div {...props} className="lk-chat">
      {publicChatPinnedMessages && (
        <div 
          className="pinned-message-bar"
          onClick={()=>{
            // scroll to the pinned message
            const messageRef = messageRefs.current[publicChatPinnedMessages.id];
            if(messageRef){
              messageRef.scrollIntoView({ behavior: "smooth", block: "center" });
            }
          }}
        >
          <div className="pinned-message-bar-left">
            <Avatar style={{ backgroundColor: getAvatarColor(publicChatPinnedMessages?.from?.identity, participantColors) }} size={30}>
              {generateAvatar(publicChatPinnedMessages?.from?.name || "Agent")}
            </Avatar>
            <span>{publicChatPinnedMessages.message.substring(0, 20) + (publicChatPinnedMessages.message.length > 20 ? "..." : "")}</span>
          </div>
          <UnpinMessageIco onClick={(e) => {
            e.stopPropagation();
            setPublicChatPinnedMessages(null);
          }} />
        </div>
      )}
      <ul 
        className={
          `lk-list lk-chat-messages 
          ${replyMessage ? "reply-essage-in-progress" : ""}
          ${uploadedfile ? "lk-chat-file-uploaded" : ""}
          ${chatMessages.length === 0 ? "lk-empty-chat-messages" : ""}`
        } 
        ref={ulRef}>
        {chatMessages.length === 0 && (
          <div className="lk-empty-chat">
            <EmptyChat />
            <span className="lk-empty-chat-title">Start a conversation</span>
            <p className="lk-empty-chat-subtitle">There are no messages here yet. Start a conversation by sending a message.</p>
          </div>
        )}
        {props.children
          ? chatMessages.map((msg, idx) =>
              cloneSingleChild(props.children, {
                entry: msg,
                key: msg.id ?? idx,
                messageFormatter,
              })
            )
          : groupedMessages.map((group, groupIdx) => (
            <div key={groupIdx} className="sender-group">
              {group.messages.map((msg, idx, allMsg) => {
                const hideTimestamp =
                  idx >= 1 && msg.timestamp - allMsg[idx - 1].timestamp < 60_000;
                return (
                  <div 
                    ref={(el) => (messageRefs.current[msg.id] = el)}
                    key={msg.id}
                    className={
                      `lk-chat-message ${highlightedMessage === msg.id ? "highlighted-message" : ""}`
                    }
                  >
                    {highlightedMessage === msg.id && <span className="highlighted-message-highlight"/>}
                    <ChatsEntry
                      entry={msg}
                      hideName={idx > 0} // Hide name for subsequent messages in the group
                      hideTimestamp={hideTimestamp}
                      messageFormatter={messageFormatter}
                      uploadedfile={uploadedfile}
                      uploadedFileName={uploadedFileName}
                      showprivatechatdrawer={showprivatechatdrawer}
                      setReplyMessage={setReplyMessage}
                      replyMessage={replyMessage}
                      chatMessages={chatMessages}
                      setChatMessages={setChatMessages}
                      scrollToRepliedChat={scrollToRepliedChat}
                      localparticipant={localparticipant}
                      className="public-chat-drawer"
                      key={msg.id ?? idx}
                      canDownloadChatAttachment={props.canDownloadChatAttachment}
                      mode="public"
                      onEditMessage={(mesg) => {
                        setEditingMessage(mesg);
                        setEditingText(mesg.message);
                        inputRef.current?.focus();
                      }}
                      onDeleteMessage={(mesg) => {
                        // Send DELETE_MESSAGE event
                        const encoder = new TextEncoder();
                        const message = encoder.encode(
                          JSON.stringify({
                            action: DataReceivedEvent.DELETE_MESSAGE,
                            id: mesg.id,
                            mode: "public"
                          })
                        );
                        localparticipant.publishData(message, { reliable: true });
                        setChatMessages(prevMessages => prevMessages.map(m => m.id === mesg.id ? { ...m, deleted: true } : m));
                      }}
                      onPinMessage={(mesg) => {
                        setPublicChatPinnedMessages(mesg);
                      }}
                    />
                  </div>
                );
              })}
              <span style={{marginLeft: "2rem", fontSize: "10px"}} className={`${
                group.from === localparticipant ? "lk-chat-sender" : "lk-chat-receiver"
              }`}>
                {(() => {
                  const lastMessage = group.messages[group.messages.length - 1];
                  const time = new Date(lastMessage.timestamp);
                  return time.toLocaleTimeString(locale, { timeStyle: "short" });
                })()}
              </span>
            </div>
          ))
          }
      </ul>
      <form
        className={`input-area lk-chat-form ${
          uploadedfile ? "file-uploaded" : null
        }`}
        onSubmit={handleSubmit}
      >
        <div className="lk-chat-form-outer">
          {replyMessage && (
            <div className="reply-message">
              <div className="reply-message-text">
                <div className="reply-message-sender">
                  <Avatar style={{ backgroundColor: "#f56a00", marginRight: "0.4rem" }} size={16}>
                    {generateAvatar(replyMessage.name)}
                  </Avatar>
                  <span>{replyMessage.name}</span>
                </div>
                <div className="reply-message-text-msg">
                  <span>
                    {/jpeg|jpg|png|gif/.test(replyMessage?.message) ? (
                      <div className="reply-message-text-image">
                        <ImageIcon />
                        {replyMessage?.message.split(" ").slice(1).join(" ").substring(0, 20) + (replyMessage?.message.length > 20 ? "..." : "")}
                      </div>
                    ) : /pdf/.test(replyMessage?.message) ? (
                      <div className="reply-message-text-image">
                        <PDFIcon />
                        {replyMessage?.message.split(" ").slice(1).join(" ").substring(0, 20) + (replyMessage?.message.length > 20 ? "..." : "")}
                      </div>
                    ) : (
                      replyMessage?.message.substring(0, 20) + (replyMessage?.message.length > 20 ? "..." : "")
                    )
                    }
                  </span>
                </div>
              </div>
              <div
                className="reply-message-close"
                onClick={() => setReplyMessage(null)}
              >
                <CloseOutlined />
              </div>
            </div>
          )}
          <textarea
            className="text-area lk-form-control lk-chat-form-input "
            ref={inputRef}
            type="text"
            placeholder="Type a message..."
            value={editingMessage ? editingText : messageText}
            onChange={(e) => {
              if(editingMessage){
                setEditingText(e.target.value);
              } else {
                setMessageText(e.target.value);
              }
            }}
            onInput={(ev) => {
              ev.stopPropagation();
              ev.target.style.height = `${ev.target.scrollHeight}px`;

              if (!ev.target.value) {
                ev.target.style.height = "41px";
              }
            }}
            onKeyDown={(ev) => {
              if (ev.key === "Enter" && !ev.shiftKey) {
                ev.stopPropagation();
                ev.preventDefault(); // Prevents a new line from being added
                handleSubmit(ev);
              }
            }}
            onKeyUp={(ev) => ev.stopPropagation()}
            {...(uploadedfile ? { ref: inputRef } : {})}
          />
          {/* {uploadedfile && !(uploadStatus==="uploading") && (
            <div className="file-pre-upload">
              <div className="file-pre-upload-box">
                {uploadedfile?.type.includes("jpeg", "jpg", "png", "gif") ? (
                  <div className="file-upload-image">
                    <ImageIcon />
                  </div>
                ) : null}
                {uploadedfile?.type.includes("pdf") ? (
                  <div className="file-upload-image">
                    <PDFIcon style={{ width: "20px", height: "20px" }} />
                  </div>
                ) : null}
                <div className="file-upload-right">
                  <span className="file-upload-right-file-name">
                    {uploadedfile?.name.length > 15
                      ? `${uploadedfile.name
                          .split(".")
                          .slice(0, -1)
                          .join(".")
                          .substring(0, 12)}...${uploadedfile.name
                          .split(".")
                          .pop()}`
                      : uploadedfile.name}
                  </span>
                  <div className="file-uplaod-right-subhead">
                    <span>
                      {uploadedfile.size > 1024 * 1024
                        ? `${(uploadedfile.size / (1024 * 1024)).toFixed(2)} MB`
                        : `${(uploadedfile.size / 1024).toFixed(2)} KB`}
                    </span>
                  </div>
                </div>
              </div>
              <div className="file-upload-end">
                <DeleteIcon 
                  onClick={() => {
                    setUploadProgress(0);
                    setUploadStatus("");
                    setUploadedFile(null);
                  }}
                />
              </div>
            </div>
          )} */}
          {uploadedfile && (
            <div
              className={`file-upload ${
                uploadProgress === 100 && "file-upload-success"
              }`}
            >
              <span className="file-upload-first">
                {uploadedfile?.type.includes(
                  "jpeg",
                  "jpg",
                  "png",
                  "gif"
                ) ? (
                  <div className="file-upload-image">
                    <ImageIcon />
                  </div>
                ) : null}
                {uploadedfile?.type.includes("pdf") ? (
                  <div className="file-upload-image">
                    <PDFIcon
                      style={{ width: "20px", height: "20px" }}
                    />
                  </div>
                ) : null}
                <div className="file-upload-right">
                  <span>
                    {uploadedfile?.name.length > 15
                      ? `${uploadedfile.name
                          .split(".")
                          .slice(0, -1)
                          .join(".")
                          .substring(0, 12)}...${uploadedfile.name
                          .split(".")
                          .pop()}`
                      : uploadedfile.name}
                  </span>
                  <div className="file-uplaod-right-subhead">
                    <span>
                      {uploadedfile.size > 1024 * 1024
                        ? `${(
                            uploadedfile.size /
                            (1024 * 1024)
                          ).toFixed(2)} MB`
                        : `${(uploadedfile.size / 1024).toFixed(2)} KB`}
                    </span>
                    <span>
                      {uploadProgress === 100 ? (
                        "Upload Successful!"
                      ) : uploadProgress === 0 ? (
                        "Upload"
                      ) : uploadStatus === "uploading" ? (
                        "Uploading..."
                      ) : (
                        <div style={{ color: "#E14045" }}>
                          Upload Failed
                        </div>
                      )}
                    </span>
                  </div>
                  <Progress
                    className="file-upload-progress"
                    percent={uploadProgress}
                    status={
                      uploadProgress === 100 ? "success" : "active"
                    }
                    // showInfo={false}
                  />
                </div>
              </span>
              <div className="file-upload-end">
                <DeleteIcon
                  onClick={() => {
                    setUploadProgress(0);
                    setUploadStatus("");
                    setUploadedFile(null);
                    // handleCancelUpload();
                  }}
                />
              </div>
            </div>
          )}
          {uploadStatus==="uploading" && (
            <div className={`file-upload ${uploadProgress === 100 && "file-upload-success"}`}>
              <span className="file-upload-first">
                {uploadedfile?.type.includes("jpeg", "jpg", "png", "gif") ? (
                  <div className="file-upload-image">
                    <ImageIcon />
                  </div>
                ) : null}
                {uploadedfile?.type.includes("pdf") ? (
                  <div className="file-upload-image">
                    <PDFIcon style={{ width: "20px", height: "20px" }} />
                  </div>
                ) : null}
                <div className="file-upload-right">
                  <span>
                    {uploadedfile?.name.length > 15
                      ? `${uploadedfile.name
                          .split(".")
                          .slice(0, -1)
                          .join(".")
                          .substring(0, 12)}...${uploadedfile.name
                          .split(".")
                          .pop()}`
                      : uploadedfile.name}
                  </span>
                  <div className="file-uplaod-right-subhead">
                    <span>
                      {uploadedfile.size > 1024 * 1024
                        ? `${(uploadedfile.size / (1024 * 1024)).toFixed(2)} MB`
                        : `${(uploadedfile.size / 1024).toFixed(2)} KB`}
                    </span>
                    <span>
                      {uploadProgress === 100 ? "Upload Successful!" : (
                        uploadProgress === 0 ? "Upload" : (
                          uploadStatus === "uploading" ? "Uploading..." : (
                            <div style={{color: "#E14045"}}>
                              Upload Failed
                            </div>
                          )
                        )
                      )}
                    </span>
                  </div>
                  <Progress 
                    className="file-upload-progress"
                    percent={uploadProgress} 
                    status={uploadProgress === 100 ? "success" : "active"}
                  />
                </div>
              </span>
              <div className="file-upload-end">
                <DeleteIcon 
                  onClick={() => {
                    setUploadProgress(0);
                    setUploadStatus("");
                    setUploadedFile(null);
                  }}
                />
              </div>
            </div>
          )}
        </div>
        <div className="lk-chat-form-buttons">
          <div
            className="lk-chat-form-upload"
            style={{ color: "white" }}
            onClick={() => handleUpload()}
          >
            <img src={UploadIcon} alt="" className="lk-chat-form-upload-icon" />
          </div>
          <div
            onClick={(e) => {
              handleSubmit(e);
            }}
            className="send-btn lk-button lk-chat-form-button"
          >
            <SendBtnIcon />
          </div>
        </div>
      </form>
    </div>
  );
}
