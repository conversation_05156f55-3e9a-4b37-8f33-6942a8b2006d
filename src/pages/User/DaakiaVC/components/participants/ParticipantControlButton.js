import React from "react";

import { DrawerState } from "../../utils/constants";
import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";
import { ReactComponent as SvgParticipantIcon } from "./icons/ParticipantIcon.svg";
import { ReactComponent as SvgParticipantBlueIcon } from "./icons/ParticpantOnIcon.svg";

// Context import
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";

export function ParticipantControlButton({
  setDrawerState,
}) {
  const { openDrawer, setOpenDrawer } = useVideoConferencesContext();
  return (
    <div
      onClick={() => {
        setOpenDrawer((prev) => prev === DrawerState.PARTICIPANTS ? null : DrawerState.PARTICIPANTS);
        setDrawerState(DrawerState.PARTICIPANTS);
      }}
      className="lk-button control-bar-button control-bar-button-icon participants-icon lk-button-group-buttons"
    >
      {openDrawer === DrawerState.PARTICIPANTS ? (
        <SvgParticipantBlueIcon />
      ) : (
        <SvgParticipantIcon />
      )}
    </div>
  );
}
