import { useState } from "react";
import { But<PERSON>, Modal } from "antd";
import { useNavigate } from "react-router-dom";
import "../../styles/LiveStreamModal.scss";
import "../../styles/index.scss";

export function MeetindEndedModal() {
  const [isMeetingEndedOpen, setIsMeetingEndedOpen] = useState(false);
  const navigator = useNavigate();

  const handleCancel = () => {
    setIsMeetingEndedOpen(false);
  };
  const handleMeetingExit = () => {
    navigator("/dashboard");
  };
  return (
    <Modal
      title={null}
      open={isMeetingEndedOpen}
      footer={null}
      onCancel={handleCancel}
      bodyStyle={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        backgroundColor: "#1c1c1e",
        color: "white",
      }}
      className="ls-modal"
    >
      <div className="ls-description primary-font">
        Thank you for joining a Daakia call. Have a good day and we wish to see
        you soon!
      </div>
      <Button className="ls-button-cancel" ghost onClick={handleMeetingExit}>
        Close
      </Button>
    </Modal>
  );
}
