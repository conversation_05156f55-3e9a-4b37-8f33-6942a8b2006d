import React, { createContext, useContext, useState, useMemo } from 'react'

const IndexContext = createContext()

export const useIndexContext = () => {
  const context = useContext(IndexContext)
  if (!context) {
    throw new Error('useIndexContext must be used within an IndexProvider')
  }
  return context
}
export function IndexProvider({ children }) {
  const [exampleState, setExampleState] = useState(null)

  const value = useMemo(() => ({
    exampleState,
    setExampleState,
  }), [exampleState])

  return (
    <IndexContext.Provider value={value}>
      {children}
    </IndexContext.Provider>
  )
}

export default IndexContext