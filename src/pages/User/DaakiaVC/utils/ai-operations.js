// Constants for operation types
export const OPERATION_TYPES = {
  MUTE_MIC: 'mic_mute_all',
  MUTE_CAMERA: 'camera_mute_all',
  MUTE_SCREENSHARE: 'screenshare_mute_all'
};

// Operation handlers mapping
export const operationHandlers = {
  [OPERATION_TYPES.MUTE_MIC]: (room, setToastNotification, setToastStatus, setShowToast) => {
    room?.localParticipant?.setMicrophoneEnabled(false);
    setToastNotification("Your mic is muted");
    setToastStatus("info");
    setShowToast(true);
  },
  [OPERATION_TYPES.MUTE_CAMERA]: (room, setToastNotification, setToastStatus, setShowToast) => {
    room?.localParticipant?.setCameraEnabled(false);
    setToastNotification("Your camera is muted");
    setToastStatus("info");
    setShowToast(true);
  },
  [OPERATION_TYPES.MUTE_SCREENSHARE]: (room, setToastNotification, setToastStatus, setShowToast) => {
    room?.localParticipant?.setScreenShareEnabled(false);
    setToastNotification("Your screenshare is stopped");
    setToastStatus("info");
    setShowToast(true);
  }
}; 