import React from "react";
import "./ShareModal.scss";
// import { Fa<PERSON><PERSON><PERSON>, FaAngleRight, FaRegFolder } from "react-icons/fa6";
// import { IoLockClosedOutline } from "react-icons/io5";
import { 
  Modal, 
  // Select 
} from "antd";
import { 
  IoIosArrowBack, 
  // IoMdClose 
  IoMdCopy
} from "react-icons/io";
import { modalNotification } from "../../../../../utils";

/**
 * @typedef {Object} ShareModalProps
 * @property {boolean} isModalOpen - Whether the modal is open
 * @property {Function} handleOk - Function to handle modal OK
 * @property {Function} handleCancel - Function to handle modal cancel
 * @property {Function} setIsModalOpen - Function to set modal open state
 * @property {Object} selectedRecording - The selected recording
 * @property {string} videoUrl - The video URL to share
 */

export default function ShareModal({
    isModalOpen,
    handleOk,
    handleCancel,
    setIsModalOpen,
    selectedRecording,
    videoUrl,
}) {
  const handleCopyLink = () => {
    if (!videoUrl) {
      modalNotification({
        type: "error",
        message: "No video URL available to copy",
      });
      return;
    }

    window.navigator.clipboard.writeText(videoUrl);
    modalNotification({
      type: "success",
      message: "Link copied to clipboard",
    });
  };

  return (
    <Modal
      title={
        <div className="share-modal-header">
          <div>
            <IoIosArrowBack
              onClick={() => setIsModalOpen(false)}
              style={{ cursor: "pointer", color: "#555555" }}
            />
            <span className="share-modal-header-title">
              Share this recording
            </span>
          </div>
          {/* <span
            className="share-modal-copy"
            onClick={() => {
              window.navigator.clipboard.writeText(
                // selectedRecording?.recording_url
                videoUrl
              );
            }}
          >
            Copy link
          </span> */}
        </div>
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      className="share-modal"
      footer={null}
    >
      <div className="share-modal-content">
        <span>Copy {selectedRecording?.title} Link</span>
        {/* <span>{videoUrl}</span> */}
        <span>
          <IoMdCopy onClick={handleCopyLink} />
        </span>
      </div>
      {/* <div className="input-section">
        <Select
          mode="tags"
          style={{ width: "100%" }}
          placeholder="Enter email addresses"
          value={emails}
          onChange={handleChange}
          tokenSeparators={[","]}
          className="invite-email"
          autoFocus={false}
          open={false}
        />
        <button className="invite-btn">Invite</button>
      </div>
      <div className="invite-users">
        <div className="user-avatars">
          <div className="user-avatar">
            <IoMdClose className="user-avatar-remove" />
            <FaCheck className="user-avatar-verified" />
          </div>
          <div className="user-avatar">
            <IoMdClose className="user-avatar-remove" />
            <FaCheck className="user-avatar-verified" />
          </div>
        </div>
        <span className="user-invited">2 people invited</span>
      </div>
      <div className="access-section">
        <span>Who has access?</span>
        <div className="access-options">
          <div className="access-option">
            <div>
              <IoLockClosedOutline />
              <div>Only those invited</div>
            </div>
            <FaAngleRight />
          </div>
          <div className="access-option">
            <div>
              <FaRegFolder />
              <div>Anyone with the link</div>
            </div>
            <div>
              <div>2 people</div>
              <FaAngleRight />
            </div>
          </div>
          <div className="access-option">
            <div>
              <IoLockClosedOutline />
              <div>Julie christ</div>
            </div>
            <div>
              <div>can view only</div>
              <FaAngleRight />
            </div>
          </div>
        </div>
      </div> */}
    </Modal>
  );
}
