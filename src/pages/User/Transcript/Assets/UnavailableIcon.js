import React from 'react';

function UnavailableIcon({ className, style, ...props }) {
  return (
    <svg 
      width="13" 
      height="13" 
      viewBox="0 0 13 13" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      {...props}
    >
      <path d="M1.98437 11.1754C1.36356 10.5758 0.868373 9.85853 0.527714 9.0655C0.187056 8.27248 0.00774522 7.41955 0.000245419 6.55648C-0.00725439 5.69342 0.157207 4.8375 0.484032 4.03867C0.810858 3.23985 1.2935 2.51411 1.90381 1.90381C2.51411 1.2935 3.23985 0.810858 4.03867 0.484032C4.8375 0.157207 5.69342 -0.00725439 6.55648 0.000245419C7.41955 0.00774522 8.27248 0.187056 9.0655 0.527714C9.85853 0.868373 10.5758 1.36356 11.1754 1.98437C12.3594 3.21029 13.0146 4.8522 12.9998 6.55648C12.9849 8.26076 12.3013 9.89104 11.0962 11.0962C9.89104 12.3013 8.26076 12.9849 6.55648 12.9998C4.8522 13.0146 3.21029 12.3594 1.98437 11.1754ZM10.2589 10.2589C11.2346 9.28314 11.7828 7.95977 11.7828 6.57987C11.7828 5.19998 11.2346 3.8766 10.2589 2.90087C9.28314 1.92514 7.95977 1.37698 6.57987 1.37698C5.19998 1.37698 3.8766 1.92514 2.90087 2.90087C1.92514 3.8766 1.37698 5.19998 1.37698 6.57987C1.37698 7.95977 1.92514 9.28314 2.90087 10.2589C3.8766 11.2346 5.19998 11.7828 6.57987 11.7828C7.95977 11.7828 9.28314 11.2346 10.2589 10.2589ZM5.92987 7.22987V5.92987H7.22987V9.82987H5.92987V7.22987ZM5.92987 3.32987H7.22987V4.62987H5.92987V3.32987Z" fill="currentColor"/>
    </svg>
  );
}

export default UnavailableIcon; 