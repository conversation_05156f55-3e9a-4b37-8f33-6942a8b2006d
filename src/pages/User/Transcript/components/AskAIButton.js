import React, { useState, useRef, useEffect } from 'react';
import { Modal, Input, Button } from 'antd';
import { SendOutlined, RobotOutlined } from '@ant-design/icons';
import './AskAIButton.scss';

function AskAIButton() {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState([]);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setMessage('');
    setChatHistory([]);
  };

  const handleSendMessage = async () => {
    if (!message.trim()) return;
    
    const userMessage = message.trim();
    setMessage('');
    setChatHistory(prev => [...prev, { type: 'user', content: userMessage }]);
    
    setLoading(true);
    try {
      // TODO: Implement actual AI API call here
      // For now, just simulate a response
      setTimeout(() => {
        setChatHistory(prev => [...prev, { 
          type: 'ai', 
          content: `This is a simulated response to: "${userMessage}"` 
        }]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      setLoading(false);
    }
  };

  return (
    <>
      <Button 
        type="default"
        size="small"
        icon={<RobotOutlined />}
        onClick={showModal}
        title="Ask AI Assistant"
        style={{ width: '52px', height: '52px', padding: 0, marginRight: '8px' }}
      />

      <Modal
        title="AI Assistant"
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        className="ask-ai-modal"
        width={500}
      >
        <div className="chat-container">
          <div className="chat-messages">
            {chatHistory.length === 0 ? (
              <div className="welcome-message">
                <h3>👋 Hi! I&apos;m your AI Assistant</h3>
                <p>Ask me anything about this meeting and I&apos;ll help you find the information you need.</p>
              </div>
            ) : (
              chatHistory.map((msg, index) => (
                <div key={index} className={`message-bubble ${msg.type}`}>
                  {msg.type === 'ai' && <div className="ai-avatar">🤖</div>}
                  <div className="message-content">{msg.content}</div>
                </div>
              ))
            )}
            {loading && (
              <div className="message-bubble ai">
                <div className="ai-avatar">🤖</div>
                <div className="message-content loading">
                  <span className="dot" />
                  <span className="dot" />
                  <span className="dot" />
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
          
          <div className="chat-input-container">
            <Input.TextArea
              placeholder="Type your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              autoSize={{ minRows: 1, maxRows: 4 }}
              className="chat-input"
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={loading}
              className="send-button"
            />
          </div>
        </div>
      </Modal>
    </>
  );
}

export default AskAIButton; 