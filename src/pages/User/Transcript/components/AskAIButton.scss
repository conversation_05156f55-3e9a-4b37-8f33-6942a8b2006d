.ask-ai-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #333;
  margin-right: 8px;

  &:hover {
    background-color: #e6e8eb;
    border-color: #bfbfbf;
  }

  .button-icon {
    font-size: 16px;
  }
}

.ask-ai-modal {
  .ant-modal-content {
    border-radius: 12px;
    padding: 0;
    height: 600px;
    display: flex;
    flex-direction: column;
  }

  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
    margin: 0;
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
  }

  .ant-modal-body {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
  }
}

.ask-ai-container {
  .question-section {
    margin-bottom: 24px;

    .question-input {
      margin-bottom: 12px;
      border-radius: 8px;
    }

    .ask-button {
      width: 100%;
      height: 40px;
      border-radius: 8px;
      font-weight: 500;
    }
  }

  .answer-section {
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    h4 {
      margin-bottom: 12px;
      color: #333;
      font-size: 16px;
    }

    .answer-content {
      color: #555;
      line-height: 1.5;
      white-space: pre-wrap;
    }
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fafafa;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .welcome-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    h3 {
      font-size: 20px;
      margin-bottom: 12px;
    }

    p {
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .message-bubble {
    display: flex;
    gap: 8px;
    max-width: 80%;

    &.user {
      align-self: flex-end;
      flex-direction: row-reverse;

      .message-content {
        background-color: #1890ff;
        color: white;
        border-radius: 18px 18px 0 18px;
      }
    }

    &.ai {
      align-self: flex-start;

      .message-content {
        background-color: white;
        color: #333;
        border-radius: 18px 18px 18px 0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    .ai-avatar {
      width: 32px;
      height: 32px;
      background-color: #f0f0f0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }

    .message-content {
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;

      &.loading {
        display: flex;
        gap: 4px;
        padding: 12px 16px;

        .dot {
          width: 8px;
          height: 8px;
          background-color: #666;
          border-radius: 50%;
          animation: bounce 1.4s infinite ease-in-out;

          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
        }
      }
    }
  }
}

.chat-input-container {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: white;
  display: flex;
  gap: 8px;
  align-items: flex-end;

  .chat-input {
    flex: 1;
    border-radius: 20px;
    padding: 8px 16px;
    resize: none;
    max-height: 120px;
  }

  .send-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
} 