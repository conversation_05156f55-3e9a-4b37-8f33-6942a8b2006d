import React from 'react';
import { Avatar } from 'antd';

function ParticipantCard({ name, role, joinTime, leaveTime, index }) {
  return (
    <div className="participant-card" style={{ 
      border: 'none',
      marginBottom: '8px',
      padding: '4px 12px',
      borderRadius: '4px',
      backgroundColor: '#F4F8F9'
    }}>
      <div className="d-flex align-items-center justify-content-between w-100">
        <div className="d-flex align-items-center">
          <Avatar
            size={40}
            className="participant-avatar"
            style={{
              backgroundColor: `hsl(${(index * 137.5) % 360}, 70%, 65%)`,
            }}
          >
            {name?.[0]}
          </Avatar>
          <div className="participant-info ms-3">
            <h6 className="mb-1">{name}</h6>
            <p className="mb-0">Role: {role === "moderator" ? "Host" : role}</p>
          </div>
        </div>
        <div className="participant-times text-end">
          <p className="mb-0">{joinTime} - {leaveTime}</p>
        </div>
      </div>
    </div>
  );
}

export default ParticipantCard; 