import React from 'react';
import { Tabs } from 'antd';

function TabHeader({ items, activeKey, onChange }) {
  return (
    <div className="tabs-header md-px-5 sm-px-1">
      <Tabs 
        items={items.map(item => ({ 
          ...item, 
          children: null,
          label: <span style={{ fontSize: '12px' }}>{item.label}</span>
        }))} 
        activeKey={activeKey}
        onChange={onChange}
        moreIcon={
          <span className="anticon anticon-ellipsis">
            <svg viewBox="64 64 896 896" focusable="false" data-icon="ellipsis" width="1em" height="1em" fill="currentColor" aria-hidden="true">
              <path d="M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z" />
            </svg>
          </span>
        }
        tabBarGutter={8}
        destroyInactiveTabPane={false}
        hideAdd
      />
    </div>
  );
}

export default TabHeader; 