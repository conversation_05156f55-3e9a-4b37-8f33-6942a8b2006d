import React, { useEffect, useRef } from 'react';

function TranscriptCard({ timestamp, content, isActive, onSeek }) {
  const cardRef = useRef(null);

  useEffect(() => {
    if (isActive && cardRef.current) {
      cardRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }, [isActive]);

  return (
    <div 
      ref={cardRef}
      className={`transcript-card ${isActive ? 'active' : ''}`}
      onClick={onSeek}
      style={{ 
        cursor: 'pointer',
        transition: 'all 0.3s ease-in-out',
        transform: isActive ? 'scale(1.02)' : 'scale(1)',
        boxShadow: isActive ? '0 4px 12px rgba(0, 0, 0, 0.1)' : 'none'
      }}
    >
      <div className="transcript-time">{timestamp}</div>
      <div className="transcript-content">{content}</div>
    </div>
  );
}

export default TranscriptCard; 