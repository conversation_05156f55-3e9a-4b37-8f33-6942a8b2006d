import React, { forwardRef, useImperativeHandle } from 'react';
import ReactPlayer from 'react-player/lazy';

const VideoPlayer = forwardRef(({ videoUrl, onProgress }, ref) => {
  const playerRef = React.useRef(null);

  useImperativeHandle(ref, () => ({
    seekTo: (time) => {
      if (playerRef.current) {
        playerRef.current.seekTo(time);
      }
    }
  }));

  return (
    <div className="transcription-container">
      <div className="transcription-player w-100" style={{ height: '500px', maxHeight: '50vh', backgroundColor: '#000' }}>
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          className="video-thumbnail"
          controls
          playing={false}
          onError={(e) => {
            console.log(e);
          }}
          width="100%"
          height="100%"
          onProgress={onProgress}
          config={{
            file: {
              attributes: {
                controlsList: "nodownload",
                disablePictureInPicture: false,
              },
            },
          }}
        />
      </div>
    </div>
  );
});

VideoPlayer.displayName = 'VideoPlayer';

export default VideoPlayer; 