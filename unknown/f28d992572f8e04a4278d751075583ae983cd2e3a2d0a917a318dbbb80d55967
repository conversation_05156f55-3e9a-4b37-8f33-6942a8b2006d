import React, { createContext, useContext, useState, useMemo } from 'react'

const ParticipantContext = createContext()

export const useParticipantContext = () => {
  const context = useContext(ParticipantContext)
  if (!context) {
    throw new Error('useParticipantContext must be used within a ParticipantProvider')
  }
  return context
}

export function ParticipantProvider({ children }) {
  // Participant-related state
  const [participantColors, setParticipantColors] = useState(new Map());

  const value = useMemo(() => ({
    participantColors,
    setParticipantColors,
  }), [participantColors])

  return (
    <ParticipantContext.Provider value={value}>
      {children}
    </ParticipantContext.Provider>
  )
}

export default ParticipantContext 